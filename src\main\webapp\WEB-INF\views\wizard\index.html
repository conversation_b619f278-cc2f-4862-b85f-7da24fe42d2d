<th:block data-layout-decorate="~{_layout/default.html}">
	<th:block layout:fragment="css">
		<link rel="stylesheet" th:href="@{vendor/prism/prism.css}" />
		<link rel="stylesheet" th:href="@{js/odf/odf.css}" />
		<link rel="stylesheet" th:href="@{vendor/jqtree/dist/themes/default/style.min.css}"/>
		<link rel="stylesheet" th:href="@{vendor/colorpicker/css/colorpicker.css}"/>
		<link rel="stylesheet" th:href="@{css/toc.css}"/>
	</th:block>

	<th:block layout:fragment="content">
		<h2 class="hidden">본문 영역</h2>

        <div class="sideMenu mScroll">
            <!--listArea-->
            <div class="listArea type2">
                <!--tab-->
                <div class="tab tab1">
                    <!--listGroup-->
                    <div class="listGroup type2">
                        <strong class="titList active">기본지도</strong>
                        <ul class="menuList type2 clearFix" style="display:block;">
                            <li>
                                <div class="helpWrap">
                                    <div class="inputArea">
                                        <div class="col">
                                            <label for="mapWidth">가로</label>
                                            <input class="mapsize" type="number" id="mapWidth" name="mapWidth" value="1100" min="0">
                                        </div>
                                        <div class="col">
                                            <label for="mapHeight">세로</label>
                                            <input class="mapsize" type="number" id="mapHeight" name="mapHeight" value="550" min="0">
                                        </div>
                                        <div class="col">
                                            <label for="mapLevel">레벨</label>
                                            <input id="mapLevel" type="number" name="mapLevel" value="11" min="7" max="23">
                                        </div>
                                    </div>
                                    <span class="tooltip" style="margin-top:20px">
                                        <span class="icoTooltip"></span>
                                        <span class="desc">유효범위<br>가로: 0-1350, 세로: 0-550, 레벨: 7-23</span>
                                    </span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                        <strong class="titList active">지도 컨트롤</strong>
                        <ul class="menuList type2" style="display:block;">
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="basemapTypControl"/>
                                    <label for="basemapTypControl">베이스맵 변경</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="zoomTypControl" />
                                    <label for="zoomTypControl">줌 생성</label>
                                </div>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="overviewMapTypControl" />
                                    <label for="overviewMapTypControl">인덱스 맵</label>
                                </div>
                            </li>
                            
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="scaleTypControl" />
                                    <label for="scaleTypControl">축척</label>
                                </div>
                            </li>
                             
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="moveTypControl" />
                                    <label for="moveTypControl">이전/다음 화면 이동</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="mousePositionTypControl" />
                                    <label for="mousePositionTypControl">마우스 좌표 표시</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="drawTypControl" />
                                    <label for="drawTypControl">그리기도구</label>
                                </div>
                            </li>
                            <!-- 
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="measureTypControl" />
                                    <label for="measureTypControl">측정도구</label>
                                </div>
                            </li>
                             -->
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="clearTypControl" />
                                    <label for="clearTypControl">그리기/측정 초기화</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="swiperTypControl" />
                                    <label for="swiperTypControl">스와이퍼</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="divideMapTypControl" />
                                    <label for="divideMapTypControl">분할지도</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="downloadTypControl" />
                                    <label for="downloadTypControl">지도 저장</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="printTypControl" />
                                    <label for="printTypControl">지도 출력</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                        <strong class="titList active">지도 제어</strong>
                        <ul class="menuList type2" style="display:block;">
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="draggTypControl" />
                                    <label for="draggTypControl">마우스 드래그로 지도 이동 막기</label>
                                </div>
                            </li>
                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="wheelTypControl" />
                                    <label for="wheelTypControl">마우스 휠로 지도 확대/축소 막기</label>
                                </div>

                            <li>
                                <div class="checkbox">
                                    <input type="checkbox" id="resizeTypControl" />
                                    <label for="resizeTypControl">브라우저  크기에  반응하는 지도</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                        <strong class="titList active">오버레이</strong>
                        <ul class="menuList type2" style="display:block;">
                            <li>
                                <div class="checkbox auto">
                                    <input type="checkbox" id="markerTypControl" />
                                    <label for="markerTypControl">마커 생성</label>
                                </div>
                                <select id="markerType">
									<option id="basicmarker">기본 마커</option>
									<option id="dragmarker">드래그 가능 마커</option>
									<option id="custommarker">커스텀 마커</option>
								</select>
                            </li>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                        <strong class="titList active">TOC</strong>
                        <ul class="menuList type2" style="display:block;">
                            <li>
                                <div class="helpWrap">
                                	<dlv class="row">
                                        <div class="checkbox">
                                            <input type="checkbox" id="tocTypControl">
                                            <label for="tocTypControl">TOC생성</label>
                                        </div>
                                    </dlv>
                                    <div class="row">
                                        <button id="tocAddLayer" type="button" class="greyType">레이어 추가</button>
                                        <!--radioGroup-->
                                        <div class="radioGroup">
                                            <div class="radio">
                                                <input type="radio" name="tocLevel" id="tocOneDepth" />
                                                <label for="tocOneDepth">1레벨</label>
                                            </div>
                                            <div class="radio">
                                                <input type="radio" name="tocLevel" id="tocTwoDepth" />
                                                <label for="tocTwoDepth">2레벨</label>
                                            </div>
                                        </div>
                                        <!--//radioGroup-->
                                        <span class="tooltip">
                                            <span class="icoTooltip"></span>
                                            <span class="desc">
                                                2레벨의 스타일 탭의 아이콘 설정은 포인트레이어,WFS 조건에서 생성됩니다.
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="helpWrap">
                                    <label for="tocGeoServerUrl" class="auto">서버 URL</label>
                                    <!-- <input id="tocGeoServerUrl" type="text" value="http://dev.kgeop.go.kr/proxy/api/map/api/map">-->
                                    <input id="tocGeoServerUrl" type="text">
                                    <span class="tooltip">
                                        <span class="icoTooltip"></span>
                                        <span class="desc">
                                        	WMS, WFS 예시 http://dev.kgeop.go.kr/proxy/api/bag/api/map
                                            WMTS 예시 http://dev.kgeop.go.kr/proxy/api/bag/api/map/wmts
                                        </span>
                                    </span>
                                </div>
                            </li>
                            <li>
                                <div class="helpWrap">
                                    <label for="tocLayerName" class="auto">서버 레이어명</label>
                                    <input id="tocLayerName" type="text">
                                    <span class="tooltip">
                                        <span class="icoTooltip"></span>
                                        <span class="desc">
                                            작업공간명:레이어명
                                        </span>
                                    </span>
                                </div>
                            </li>
                            <li>
                                <label for="tocSetLayerName" class="auto">레이어명 설정</label>
                                <input id="tocSetLayerName" type="text" value="기본 레이어명">
                            </li>
                            <!-- <li>
                                <label for="tocGeometryType" class="auto">지오메트리 타입</label>
                                <select id="tocGeometryType">
                                    <option id="1">Point</option>
									<option id="2">Linestring</option>
									<option id="3">Polygon</option>
                                </select>
                                <span class="tooltip">
                                    <span class="icoTooltip"></span>
                                    <span class="desc">
                                        지오메트리 타입이 일치해야 합니다.
                                    </span>
                                </span>
                            </li> -->
                            <li>
                                <label for="tocLayerType" class="auto">레이어 타입</label>
                                <select id="tocLayerType">
                                    <option id="wms">wms</option>
									<option id="wfs">wfs</option>
									<option id="wmts">wmts</option>
                                </select>
                            </li>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                        <strong class="titList active">레이어</strong>
                        <ul class="menuList type2" style="display:block;">
                            <li>
                                <div class="checkbox auto">
                                    <input type="checkbox" id="layerTypControl" />
                                    <label for="layerTypControl">레이어 추가</label>
                                </div>
                                <select id="serverType">
									<option id="geoserver">GeoServer</option>
									<option id="geojson">GeoJson</option>
								</select> 
								<select id="layerType" style="display: none">
									<option id="default">타입선택</option>
									<option id="point">Point</option>
									<option id="linestring">LineString</option>
									<option id="polygon">Polygon</option>
								</select>
                            </li>
                            <div id="geoServerOpt">
                            <li>
                            	<div class="helpWrap">
                                <label for="geoServerUrl" class="auto">서버 URL</label>
                                <input id="geoServerUrl" type="text">
                                <span class="tooltip">
                                    <span class="icoTooltip"></span>
                                    <span class="desc">WMS, WFS 예시 http://dev.kgeop.go.kr/proxy/api/bag/api/map
                                            WMTS 예시 http://dev.kgeop.go.kr/proxy/api/bag/api/map/wmts</span>
                                </span>
                                </div>
                            </li>
                            <li>
                            	<div class="helpWrap">
                                <label for="layerName" class="auto">레이어 명</label>
                                <input id="layerName" type="text">
                                <span class="tooltip">
                                    <span class="icoTooltip"></span>
                                    <span class="desc">저장소명:레이어명</span>
                                </span>
                                </div>
                            </li>
                            <li>
                                <label for="serverLayerType" class="auto">레이어 타입</label>
                                <select id="serverLayerType">
                                    <option id="wms">wms</option>
									<option id="wfs">wfs</option>
									<option id="wmts">wmts</option>
                                </select>
                            </li>
                            </div>
                        </ul>
                    </div>
                    <!--//listGroup-->
                    <!--listGroup-->
                    <div class="listGroup">
                    	<div class="helpWrap">
	                        <strong class="titList active">스타일
	                        	<span class="tooltip strong">
		                            <span class="icoTooltip"></span>
		                            <span class="desc">스타일 추가를 위해서는 레이어 생성을 먼저 해주세요. <br>
		                            GeoServer 레이어의 경우 WFS 레이어만 스타일 적용이 가능합니다.<br>
		                            GeoServer 레이어의 경우 스타일 Geometry 와 일치 시에만 적용됩니다.</span>
		                        </span>
	                        </strong>
	                        </div>
	                    
	                        <ul class="menuList type2" style="display:block;">
	                            <li>
	                                <div class="checkbox auto">
	                                    <input type="checkbox" id="styleTypControl" />
	                                    <label for="styleTypControl">스타일 추가</label>
	                                </div>
	                                <select id="styleType" class="styleChangeOption">
										<option id="point">점</option>
										<option id="linestring">선</option>
										<option id="polygon">면</option>
									</select>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">&lt;스타일 옵션&gt;</span></dt>
	                                </dl>
	                                <dd>
	                                    <label for="pointRadius" class="auto">점크기</label>
	                                    <input id="pointRadius" type="number" class="styleChangeOption" value="25" min="1" max="50">
	                                </dd>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">선 옵션</span></dt>
	                                </dl>
	                                <dd>
	                                    <label for="lineStyleSelectBox" class="auto">선 스타일</label>
		                                <select id="lineStyleSelectBox" class="styleChangeOption">
		                                    <option id="solid" value="[0, 0]">solid</option>
											<option id="dash" value="[3, 15]">dash</option>
		                                </select>
	                                </dd>
	                                <dd>
	                                    <label for="lineCapSelectBox" class="auto">선의 모양</label>
		                                <select id="lineCapSelectBox" class="styleChangeOption">
		                                    <option id="square" value="square">square</option>
											<option id="round" value="round">round</option>
											<option id="butt" value="butt">butt</option>
		                                </select>
	                                </dd>
	                                <dd>
	                                    <label for="lineJoinSelectBox" class="auto">접선 모양</label>
		                                <select id="lineJoinSelectBox" class="styleChangeOption">
		                                    <option id="round" value="round">round</option>
											<option id="bevel" value="bevel">bevel</option>
											<option id="miter" value="miter">miter</option>
		                                </select>
	                                </dd>
	                                <dd>
	                                    <label for="lineThickInputBox" class="auto">점크기</label>
	                                    <input id="lineThickInputBox" type="number" class="styleChangeOption"  name="lineThickInputBox" value="10" min="1" max="20">
	                                </dd>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">선색</span></dt>
	                                </dl>
	                                <dd>
		                                <div class="helpWrap">
		                                    <div class="inputArea">
		                                        <div class="col" style="width:20%;">
		                                            <label for="lineColorRedInputBox">R:</label>
		                                            <input id="lineColorRedInputBox" type="number" class="styleChangeOption" name="lineColorRedInputBox" value="150" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="lineColorGreenInputBox">G:</label>
		                                            <input id="lineColorGreenInputBox" type="number" class="styleChangeOption" name="lineColorGreenInputBox" value="37" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="lineColorBlueInputBox">B:</label>
		                                            <input id="lineColorBlueInputBox" type="number" class="styleChangeOption" name="lineColorBlueInputBox" value="250" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="lineColorOpacityInputBox">투명도:</label>
		                                            <input id="lineColorOpacityInputBox" type="number" class="styleChangeOption" name="lineColorOpacityInputBox" value="1" step="0.1" min="0.1" max="1" />
		                                        </div>
		                                    </div>
		                                </div>
	                                </dd>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">채우기색</span></dt>
	                                </dl>
	                                <dd>
		                                <div class="helpWrap">
		                                    <div class="inputArea">
		                                        <div class="col" style="width:20%;">
		                                            <label for="fillColorRedInputBox">R:</label>
		                                            <input id="fillColorRedInputBox" type="number" class="styleChangeOption" name="fillColorRedInputBox" value="168" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fillColorGreenInputBox">G:</label>
		                                            <input id="fillColorGreenInputBox" type="number" class="styleChangeOption" name="fillColorGreenInputBox" value="123" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fillColorBlueInputBox">B:</label>
		                                            <input id="fillColorBlueInputBox" type="number" class="styleChangeOption" name="fillColorBlueInputBox" value="250" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fillColorOpacityInputBoxBasic">투명도:</label>
		                                            <input id="fillColorOpacityInputBoxBasic" type="number" class="styleChangeOption" name="fillColorOpacityInputBoxBasic" value="0.6" step="0.1" min="0.1" max="1" />
		                                        </div>
		                                    </div>
		                                </div>
	                                </dd>
	                            </li>
	                            <li>
	                                <div class="checkbox auto">
	                                	<br>
	                                    <input id="textUseYn" name="textUseYn" type="checkbox" class="styleChangeOption" />
	                                    <label for="textUseYn">텍스트 사용</label>
	                                </div>
	                                <span class="tooltip">
	                                    <span class="icoTooltip"></span>
	                                    <span class="desc">스타일이 생성되어 있는 경우에 적용 가능합니다.</span>
	                                </span>
	                            </li>
	                        </ul>
                    </div>
                    <div class="listGroup">
						<ul id="textOptions" class="menuList type2 styleChangeOption" style="margin-top:10px;display:none">
	                        	<li>
	                                <div class="helpWrap">
	                                    <label for="text" class="auto type2">내용</label>
	                                    <input id="text" name="text" type="text" class="styleChangeOption" style="width:203px;" maxlength="20" placeholder="텍스트를 입력하세요">
	                                </div>
	                            </li>
	                            <li>
	                                <div class="helpWrap">
	                                    <label for="text" class="auto type2">폰트</label>
	                                    <div class="colGroup">
                                            <div class="col">
                                                <select id="font" class="styleChangeOption">
													<option id="normal" value="normal">normal</option>
													<option id="bold" value="bold">굵게</option>
												</select>
                                            </div>
                                            <div class="col">
                                                <input type="number" class="styleChangeOption" id="fontSize" name="fontSize" value="15" min="1" max="100">
                                            </div>
                                            <div class="col">
                                                <select id="fontFamily" class="styleChangeOption">
													<option id="Arial" value="Arial">Arial</option>
													<option id="vernada" value="vernada">vernada</option>
													<option id="Times New Roman" value="Times New Roman">Times New Roman</option>
													<option id="Georgia" value="Georgia">Georgia</option>
													<option id="Tahoma" value="Tahoma">Tahoma</option>
													<option id="Gulim" value="Gulim">굴림</option>
													<option id="Dotum" value="Dotum">돋움</option>
												</select>
                                            </div>
                                        </div>
                                        <div class="row">
                                        	<div class="checkbox">                                        	
												<input id="obliqueYn" type="checkbox" class="styleChangeOption">
												<label for="obliqueYn">글자 기울임</label>
                                        	</div>
										</div>
	                                </div>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">글자색</span></dt>
	                                </dl>
	                                <dd>
		                                <div class="helpWrap">
		                                    <div class="inputArea">
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontColorRedInputBox">R:</label>
		                                            <input id="fontColorRedInputBox" type="number" class="styleChangeOption" name="fontColorRedInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontColorGreenInputBox">G:</label>
		                                            <input id="fontColorGreenInputBox" type="number" class="styleChangeOption" name="fontColorGreenInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontColorBlueInputBox">B:</label>
		                                            <input id="fontColorBlueInputBox" type="number" class="styleChangeOption" name="fontColorBlueInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontColorOpacityInputBox">투명도:</label>
		                                            <input id="fontColorOpacityInputBox" type="number" class="styleChangeOption" name="fontColorOpacityInputBox" value="1" step="0.1" min="0.1" max="1" />
		                                        </div>
		                                    </div>
		                                </div>
	                                </dd>
	                            </li>
	                            <li>
	                                <dl>
	                                    <dt><span class="option">글자 테두리색</span></dt>
	                                </dl>
	                                <dd>
		                                <div class="helpWrap">
		                                    <div class="inputArea">
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontLineColorRedInputBox">R:</label>
		                                            <input id="fontLineColorRedInputBox" type="number" class="styleChangeOption" name="fontLineColorRedInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontLineColorGreenInputBox">G:</label>
		                                            <input id="fontLineColorGreenInputBox" type="number" class="styleChangeOption" name="fontLineColorGreenInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontLineColorBlueInputBox">B:</label>
		                                            <input id="fontLineColorBlueInputBox" type="number" class="styleChangeOption" name="fontLineColorBlueInputBox" value="0" min="0" max="255" />
		                                        </div>
		                                        <div class="col" style="width:20%;">
		                                            <label for="fontLineColorOpacityInputBox">투명도:</label>
		                                            <input id="fontLineColorOpacityInputBox" type="number" class="styleChangeOption" name="fontLineColorOpacityInputBox" value="1" step="0.1" min="0.1" max="1" />
		                                        </div>
		                                    </div>
		                                </div>
	                                </dd>
	                            </li>
	                            <li>
	                                <div class="helpWrap">
	                                    <label for="textOffsetX" class="auto">X축 위치 변경</label>
	                                    <input id="textOffsetX" name="textOffsetX" type="number" class="styleChangeOption" value="0" >
	                                </div>
	                                <div class="helpWrap">
	                                    <label for="textOffsetY" class="auto">Y축 위치 변경</label>
	                                    <input id="textOffsetY" name="textOffsetY" type="number" class="styleChangeOption" value="0" >
	                                </div>
	                            </li>
	                            <li>
	                            	<div class="helpWrap">
		                                <label for="textPlacement" class="auto type3">line/point에 맞춘 텍스트</label>
		                                <select id="textPlacement" class="styleChangeOption" style="width:125px;">
											<option id="point" value="point">point</option>
											<option id="line" value="line">line</option>
										</select>
		                                <span class="tooltip">
	                                        <span class="icoTooltip"></span>
	                                        <span class="desc">
	                                            Line 옵션은 Point 지오메트리 타입에서는 제공하지 않습니다.
	                                        </span>
	                                    </span>
	                                </div>
	                            </li>
	                            <li>
	                            	<div class="helpWrap">
		                                <label for="textRotation" class="auto" style="width:40px;">각도</label>
		                                <input id="textRotation" name="textRotation" type="number" class="styleChangeOption inline" value="0" style="width: 40px;">
	                                </div>
	                            </li>
	                        </ul>
                    </div>
                    
                    <!--//listGroup-->
                </div>
                <!--//tab-->
            </div>
            <!--//listArea-->
        </div>

		<div id="content" class="mScroll">
			<div class="btnGroup">
                <button type="button" class="btnTop"><span class="hidden">맨 위로</span></button>
                <button type="button" class="btnBottom"><span class="hidden">맨 아래로</span></button>
            </div>
			<div id="map-doc" class="innerContent">
				<section>
                    <h3 class="titSec">Wizard</h3>
                    <!--row-->
                    <div class="row">
                        <div class="mapArea">
                            <div id="wizard-content" class="container"></div>
                            <div id="coordDiv"></div>
                        </div>
                    </div>
                    <!--//row-->
                </section>
                <section>
                    <h3 class="titSec">샘플 코드</h3>
                    <div class="posBtn">
                        <button id="showLive" type="button" class="whiteType try">직접해보기</button>
                    </div>
                    <!--row-->
                    <div class="row">
                        <div class="codeArea large">
                            <pre>
								<code id="wizard-code" class="line-numbers language-markup">
								</code>
							</pre>
                        </div>
                     </div>
                 </section>
			</div>
		</div>

	</th:block> 

	<!-- index.html 고유 스크립트 추가 -->
	<th:block layout:fragment="script-bottom">
		<script>
			var DeveloperUrl = location.protocol + "//" + location.host + '[[${@egovProperties.getProperty('Url.ContextPath')}]]';
			var GeoserverUrl = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var pointLayer = '[[${@egovProperties.getProperty('Layer.PointLayer')}]]';
			var OdfUrl = DeveloperUrl + '/js/odf';
            var proxyUseAt = '[[${@egovProperties.getProperty('Sample.Proxy.Use')}]]';
			
			// 개발자지원센터 지도마법사 설정값
			var baroEMapURL = '[[${@egovProperties.getProperty('Url.BaroEMapURL')}]]';
			var baroEMapAirURL = '[[${@egovProperties.getProperty('Url.BaroEMapAirURL')}]]';
            var baroEMapKey = '[[${@egovProperties.getProperty('Url.BaroEMapKey')}]]';
            var sampleSrid = '[[${@egovProperties.getProperty('Sample.map.Srid')}]]';
			var sampleBasemap = '[[${@egovProperties.getProperty('Sample.Basemap')}]]';
			var customBasemapConfig = '[[${@egovProperties.getProperty('Sample.Basemap.Custom.Config')}]]';
			
			$('#tocGeoServerUrl').val(GeoserverUrl);
			$('#tocLayerName').val(pointLayer);
			$('#geoServerUrl').val(GeoserverUrl);
			$('#layerName').val(pointLayer);
		</script>
		<!-- <script th:src="${'/js/odf'+'/odf.min.js'}"></script> -->
		
		<script th:src="@{js/odf/odf.min.js}"></script>  
		<script th:src="@{vendor/clipboard/clipboard.js}"></script>
		<script th:src="@{vendor/prism/prism.js}"></script>
		<script th:src="@{vendor/jqtree/dist/jstree.min.js}"></script>
		<script th:src="@{vendor/colorpicker/js/colorpicker.js}"></script>
		<script th:src="@{js/sample/toc.js(ms=${ms})}"></script>
		<script th:src="@{js/wizard/index.js(ms=${ms})}"></script>
	</th:block>

</th:block>