#-----------------------------------------------------------------------
#
#   globals.properties : \uC2DC\uC2A4\uD15C
#
#-----------------------------------------------------------------------
#   1.  key = value \uAD6C\uC870\uC785\uB2C8\uB2E4.
#   2.  key\uAC12\uC740 \uACF5\uBC31\uBB38\uC790\uB97C \uD3EC\uD568\uBD88\uAC00, value\uAC12\uC740 \uACF5\uBC31\uBB38\uC790\uB97C \uAC00\uB2A5
#   3.  key\uAC12\uC73C\uB85C \uD55C\uAE00\uC744 \uC0AC\uC6A9\uBD88\uAC00,   value\uAC12\uC740 \uD55C\uAE00\uC0AC\uC6A9\uC774 \uAC00\uB2A5
#   4.  \uC904\uC744 \uBC14\uAFC0 \uD544\uC694\uAC00 \uC788\uC73C\uBA74 '\'\uB97C \uB77C\uC778\uC758 \uB05D\uC5D0 \uCD94\uAC00(\uB9CC\uC57D  '\'\uBB38\uC790\uB97C \uC0AC\uC6A9\uD574\uC57C \uD558\uB294 \uACBD\uC6B0\uB294 '\\'\uB97C \uC0AC\uC6A9)
#   5.  Windows\uC5D0\uC11C\uC758 \uB514\uB809\uD1A0\uB9AC \uD45C\uC2DC : '\\' or '/'  ('\' \uC0AC\uC6A9\uD558\uBA74 \uC548\uB428)
#   6.  Unix\uC5D0\uC11C\uC758 \uB514\uB809\uD1A0\uB9AC \uD45C\uC2DC : '/'
#   7.  \uC8FC\uC11D\uBB38 \uCC98\uB9AC\uB294  #\uC0AC\uC6A9
#   8.  value\uAC12 \uB4A4\uC5D0 \uC2A4\uD398\uC774\uC2A4\uAC00 \uC874\uC7AC\uD558\uB294 \uACBD\uC6B0 \uC11C\uBE14\uB9BF\uC5D0\uC11C \uCC38\uC870\uD560\uB54C\uB294 \uC5D0\uB7EC\uBC1C\uC0DD\uD560 \uC218 \uC788\uC73C\uBBC0\uB85C trim()\uD558\uAC70\uB098 \uB9C8\uC9C0\uB9C9 \uACF5\uBC31\uC5C6\uC774 properties \uAC12\uC744 \uC124\uC815\uD560\uAC83
#-----------------------------------------------------------------------
#\uC77C\uBC18
#Url.APIGW = ${GEONPAAS_GATEWAY_URL:http://geon.wavus.co.kr:14062}
Url.APIGW = http://geon.wavus.co.kr:14062
Url.Geoserver = http://geon.wavus.co.kr:14066/geoserver

Url.ContextPath =
Url.WfsAPI =  /api/map/wfs
Url.WmsAPI = /api/map/wms
Url.WmtsAPI = /api/map/wmts

Url.DOCS = /docs/api/
#\uB18D\uD611 SWAGGER \uD638\uCD9C \uBD84\uAE30\uCC98\uB9AC\uC6A9
Type.DOCS = NORMAL
#\uC77C\uBC18 KONG SWAGGER \uD638\uCD9C \uBD84\uAE30\uCC98\uB9AC\uC6A9
#Type.DOCS = NORMAL

#\uC0D8\uD50C\uC5D0 \uD504\uB85D\uC2DC \uC0AC\uC6A9\uD560\uC9C0 \uC5EC\uBD80(Y, N) N \uC73C\uB85C \uC124\uC815\uC2DC \uD504\uB85D\uC2DC \uC124\uC815\uBD80\uBD84 \uC8FC\uC11D\uCC98\uB9AC\uB429\uB2C8\uB2E4.
Sample.Proxy.Use = Y
#\uC0D8\uD50C\uC5D0 \uC0AC\uC6A9\uD560 \uBC30\uACBD\uC9C0\uB3C4 (VWORLD, BAROEMAP, CUSTOM)
Sample.Basemap = CUSTOM
#CUSTOM \uBC30\uACBD\uC9C0\uB3C4 \uC124\uC815 - \uAE30\uBCF8\uAC12 \uC0AC\uC6A9 (basemap-config.js\uC5D0\uC11C \uAE30\uBCF8\uAC12 \uC81C\uACF5)
Sample.Basemap.Custom.Config =
#\uC9C0\uB3C4 \uC608\uC81C SRID (\uC804\uCCB4 \uC608\uC81C \uB3D9\uC77C\uD558\uAC8C \uC801\uC6A9\uB428 -> 5179, 5186)
Sample.map.Srid = 5186
#\uC704\uC82F \uC608\uC81C SRID (\uC804\uCCB4 \uC608\uC81C \uB3D9\uC77C\uD558\uAC8C \uC801\uC6A9\uB428 -> 5179, 5186)
Sample.widget.Srid = 5186
#\uD14C\uC2A4\uD2B8 \uC720\uC800 id
Sample.UserId = geonuser


#\uC0D8\uD50C\uC6A9 url
Url.BaroEMapURL = http://geon.wavus.co.kr:14062/map/api/map/baroemap
Url.BaroEMapAirURL = http://geon.wavus.co.kr:14062/map/api/map/ngisair
#Url.BaroEMapKey = 3FA3CDEA6C6EAAA78FDBF09F84A91EA7
Url.VWorldURL =http://geon.wavus.co.kr:14062/map/api/vworld/wmts
#odf \uC0D8\uD50C \uB808\uC774\uC5B4
Layer.PointLayer = geonpaas:L100000256
Layer.LineLayer = geonpaas:L100000255
Layer.PolygonLayer1 = geonpaas:L100000254
Layer.PolygonLayer2 = geonpaas:L100000258
Layer.WmtsLayer = geonpaas:L100000252
Layer.HotspotLayer = geonpaas:L100001001

#oui \uC0D8\uD50C \uB808\uC774\uC5B4 (PointLayer1 : [\uC810]\uC81C\uC8FC\uB3C4 \uC804\uAE30\uCC28 \uCDA9\uC804\uC18C / LineLayer1 : [\uC120]\uC81C\uC8FC\uB3C4 \uC790\uC804\uAC70\uAE38 / PolygonLayer1 : [\uBA74]\uC81C\uC8FC\uB3C4 \uC11C\uADC0\uD3EC\uC2DC \uC74D\uBA74\uB3D9)
Layer.TestPointLayer1 = Wgeonuser:L100000696
Layer.TestPointLayer1Id = LR0000000541
Layer.TestPointLayer1Nm = [\uC810]\uC81C\uC8FC\uB3C4 \uC804\uAE30\uCC28 \uCDA9\uC804\uC18C
Layer.TestLineLayer1 =  Wgeonuser:L100000556
Layer.TestLineLayer1Id = LR0000000408
Layer.TestLineLayer1Nm = [\uC120]\uC81C\uC8FC\uB3C4 \uC790\uC804\uAC70\uAE38
Layer.TestPolygonLayer1 = Wgeonuser:L100000516
Layer.TestPolygonLayer1Id = LR0000000374
Layer.TestPolygonLayer1Nm = [\uBA74]\uC81C\uC8FC\uB3C4 \uC11C\uADC0\uD3EC\uC2DC \uC74D\uBA74\uB3D9
#oui \uD3B8\uC9D1\uB54C \uC0AC\uC6A9\uB418\uB294 \uB808\uC774\uC5B4 ([\uBA74]\uC81C\uC8FC\uB3C4 \uC81C\uC8FC\uC2DC \uD589\uC815\uAD6C\uC5ED)
Layer.TestEditPolygonLayer1 = Wgeonuser:L100000577
Layer.TestEditPolygonLayer1Id = LR0000000425
Map.UserMapId = UM0000000212


#\uC77C\uBC18
#Service.API = map,smt,analysis,coord,addrgeo
#\uB18D\uD611
#Service.API = :14110,:14120,:14130
Service.API = smt,map,analysis,publish,coord,addrgeo
#API \uC11C\uBE44\uC2A4
#1.\uC800\uC791\uB3C4\uAD6C
Service.smt=http://geon.wavus.co.kr:14062/smt
#2.\uC9C0\uB3C4\uAD00\uB9AC
Service.map= http://geon.wavus.co.kr:14062/map
#3.\uB3C4\uD615\uBD84\uC11D
Service.analysis= http://geon.wavus.co.kr:14062/analysis
#4.\uB3C4\uD615\uC815\uBCF4 \uB4F1\uB85D
Service.publish= http://geon.wavus.co.kr:14062/publish
#5.\uC88C\uD45C\uBCC0\uD658 API
Service.coord= http://geon.wavus.co.kr:14062/coord
#6.\uC9C0\uC624\uCF54\uB529
Service.addrgeo= http://geon.wavus.co.kr:14062/addrgeo

## \uC6CC\uD06C\uD50C\uB85C\uC6B0, \uB85C\uADF8, \uB808\uC774\uC5B4\uADF8\uB8F9 API \uBBF8\uC0AC\uC6A9\uC73C\uB85C \uAD00\uB828 \uCF54\uB4DC \uC8FC\uC11D\uCC98\uB9AC
#7.\uC6CC\uD06C\uD50C\uB85C\uC6B0 API
#Service.workflow= http://geon.wavus.co.kr:14062/workflow
#8.\uB85C\uADF8 API
#Service.log= http://geon.wavus.co.kr:14062/log
#9.\uB808\uC774\uC5B4\uADF8\uB8F9 API
#Service.layerGroup= http://geon.wavus.co.kr:14062/lyrgroup

#,cdr,bag,est,ctt,adg
Service.API.Order = API \uC0AC\uC6A9 \uC815\uBCF4 \uAD00\uB9AC,\uACF5\uD1B5 \uCF54\uB4DC \uC870\uD68C,\uB808\uC774\uC5B4 \uC18D\uC131\uD544\uD130 \uC124\uC815,\uB808\uC774\uC5B4 \uC2A4\uD0C0\uC77C \uC124\uC815,\uB808\uC774\uC5B4 \uC815\uBCF4 \uAD00\uB9AC,\uB808\uC774\uC5B4 \uC18D\uC131 \uC815\uBCF4 \uAD00\uB9AC,\uB808\uC774\uC5B4 \uCEEC\uB7FC \uC815\uBCF4 \uAD00\uB9AC,\uB808\uC774\uC5B4 \uD31D\uC5C5 \uC124\uC815,\uB85C\uADF8\uC778 \uAD00\uB9AC,\uBCA0\uC774\uC2A4\uB9F5 \uAD00\uB9AC,\uC0AC\uC6A9\uC790 \uBD81\uB9C8\uD06C \uAD00\uB9AC,\uC0AC\uC6A9\uC790 \uC774\uBBF8\uC9C0 \uAD00\uB9AC,\uC0AC\uC6A9\uC790 \uC9C0\uB3C4 \uC791\uC5C5 \uC54C\uB9BC \uAD00\uB9AC,\uC6F9\uB808\uC774\uC5B4\uC11C\uBE44\uC2A4 \uAD00\uB9AC,\uC6F9\uB9F5 \uAD00\uB9AC,\uC6F9\uC571 \uD15C\uD50C\uB9BF \uAD00\uB9AC,\uC9C0\uB3C4 TOC \uAD00\uB9AC,\uC9C0\uC624\uCF54\uB529 \uACB0\uACFC \uD30C\uC77C \uAD00\uB9AC,\uC800\uC7A5\uC18C \uC815\uBCF4 \uAD00\uB9AC,\uD14C\uC774\uBE14 \uB808\uC774\uC5B4 \uC815\uBCF4 \uAD00\uB9AC,\
                    \uC9C0\uB3C4 \uC694\uCCAD,\uCEE8\uD150\uCE20\uAD00\uB9AC,\uD3EC\uD0C8 \uC694\uCCAD,\
                    \uB3C4\uD615\uBD84\uC11D-\uACF5\uAC04\uD328\uD134 \uBD84\uC11D,\uB3C4\uD615\uBD84\uC11D-\uADFC\uC811\uB3C4 \uBD84\uC11D,\uB3C4\uD615\uBD84\uC11D-\uB370\uC774\uD130 \uAD00\uB9AC \uBD84\uC11D,\uB3C4\uD615\uBD84\uC11D-\uB370\uC774\uD130 \uC694\uC57D \uBD84\uC11D,\uB3C4\uD615\uBD84\uC11D-\uC704\uCE58\uCC3E\uAE30 \uBD84\uC11D,\uB808\uC774\uC5B4 \uD30C\uC77C \uB2E4\uC6B4\uB85C\uB4DC,\uC544\uD2C0\uB780-Api,\uC791\uC5C5\uC54C\uB9BC,\
                    \uB3C4\uD615\uC815\uBCF4 \uC5C5\uB85C\uB4DC,\uB808\uC774\uC5B4 \uAD00\uB9AC,\
                    \uC88C\uD45C\uBCC0\uD658,\
                    \uC704\uCE58\uAC80\uC0C9,\uC8FC\uC18C\uC815\uC81C,\uC9C0\uC624\uCF54\uB529,\uD589\uC815\uAD6C\uC5ED \uAC80\uC0C9,\
                    \uB77C\uC774\uBE0C\uB7EC\uB9AC \uC815\uBCF4 \uAD00\uB9AC,\uB77C\uC774\uBE0C\uB7EC\uB9AC \uD30C\uB77C\uBBF8\uD130 \uC815\uBCF4 \uAD00\uB9AC,\uC6CC\uD06C\uD50C\uB85C\uC6B0 \uACF5\uC720 \uAD00\uB9AC,\uC6CC\uD06C\uD50C\uB85C\uC6B0 \uB77C\uC774\uBE0C\uB7EC\uB9AC \uAD00\uB9AC,\uC6CC\uD06C\uD50C\uB85C\uC6B0 \uC774\uB825 \uAD00\uB9AC,\uC6CC\uD06C\uD50C\uB85C\uC6B0 \uC815\uBCF4 \uAD00\uB9AC,\uB808\uC774\uC5B4\uADF8\uB8F9 \uC815\uBCF4

#,\uC791\uC5C5\uC54C\uB9BC,\uB808\uC774\uC5B4 \uAD00\uB9AC,\uCEE8\uD150\uCE20 \uC815\uBCF4,\uC9C0\uB3C4 \uC694\uCCAD,\
\uC8FC\uC18C\uAC80\uC0C9,\uC8FC\uC18C\uC815\uC81C,\uC9C0\uC624\uCF54\uB529,\uC88C\uD45C\uBCC0\uD658,\uACF5\uAC04\uC815\uBCF4 \uD30C\uC77C \uB2E4\uC6B4\uB85C\uB4DC,\
				\uB370\uC774\uD130\uCD94\uCD9C,\uC77C\uD544\uC9C0 \uC885\uD569 \uC815\uBCF4,\uD1A0\uC9C0 \uC870\uD68C(\uC77C\uD544\uC9C0),\uAC74\uBB3C \uC870\uD68C(\uC77C\uD544\uC9C0),\uAC00\uACA9 \uC815\uBCF4 \uC870\uD68C(\uC77C\uD544\uC9C0),\uD1A0\uC9C0\uC774\uC6A9\uACC4\uD68D \uC870\uD68C(\uC77C\uD544\uC9C0),\
				\uB3C4\uD615\uC601\uC5ED\uC73C\uB85C \uAC80\uC0C9,\uD589\uC815\uACBD\uACC4\uB85C \uAC80\uC0C9,\uAD6D\uAC00\uBC95\uB839\uC815\uBCF4,\uC815\uBD80\uB514\uB809\uD130\uB9AC,SMS \uBA54\uC2DC\uC9C0
Service.Crtfckey = tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh

#Kakao Key
#AppKey.Kakao = b064950d475be361fd1e97aed8644716
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276

#Naver Key
AppKey.Naver = vlf2u84az9

#Google Key
AppKey.Google = AIzaSyCHW_Vs6mcwzRZWYpFxe1EgQ46DNDAPxeA

#\uBE0C\uC774\uC6D4\uB4DC Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr

#\uAD6D\uAC00\uACF5\uAC04\uC815\uBCF4 \uD3EC\uD138 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e

#\uAD6D\uAC00\uAD50\uD1B5\uC815\uBCF4\uC13C\uD130 Key, Url (CCTV \uC704\uC82F)
Ntic.ApiKey = 46d62a1ce207492aa55ee90017079639
Ntic.Url = https://openapi.its.go.kr:9443

Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
