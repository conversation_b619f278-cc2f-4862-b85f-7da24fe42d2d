/**
 * 배경지도 설정 통합 관리
 * BAROEMAP, VWORLD, CUSTOM 배경지도 설정을 중앙화하여 관리합니다.
 */

// 배경지도 설정 객체
window.BasemapConfigs = {
    BAROEMAP: {
        type: 'baroEMap',
        maps: {
            base: 'eMapBasic',
            air: 'eMapAIR',
            white: 'eMapWhite',
            color: 'eMapColor',
            etc: 'eMapWhiteEdu'
        },
        option: function (baroEMapURL, baroEMapAirURL, baroEMapKey) {
            return `baroEMapURL : '${baroEMapURL}',
		baroEMapAirURL : '${baroEMapAirURL}',${baroEMapKey ? `
        baroEMapKey : '${baroEMapKey}',` : ''}
		basemap : {
			baroEMap : ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'],
		},
    }`;
        },
        replacement: {
            from: "baroEMap:[eMapBasic,eMapAIR,eMapColor,eMapWhite,eMApWhiteEdu]",
            to: "baroEMap:['eMapBasic','eMapAIR','eMapColor','eMapColor','eMapWhite','eMapWhiteEdu']"
        },
        airOption: {
            basemap: "baroEMap:['eMapAIR']",
            urlDefine: function (baroEMapAirURL) {
                return `baroEMapAirURL : '${baroEMapAirURL}'`;
            }
        }
    },

    VWORLD: {
        type: 'vWorld',
        maps: {
            base: 'vWorldBase',
            air: 'vWorldSatellite',
            white: 'vWorldWhite',
            color: 'vWorldMidnight',
            etc: 'vWorldHybrid'
        },
        option: function (vWorldURL) {
            return `vWorldURL : '${vWorldURL}',
		basemap : {
			vWorld : ['vWorldBase', 'vWorldWhite', 'vWorldMidnight', 'vWorldHybrid', 'vWorldSatellite'],
		},
    }`;
        },
        replacement: {
            from: "vWorld:[vWorldBase,vWorldWhite,vWorldMidnight,vWorldHybrid,vWorldSatellite]",
            to: "vWorld:['vWorldBase','vWorldWhite','vWorldMidnight','vWorldHybrid','vWorldSatellite']"
        },
        airOption: {
            basemap: "vWorld:['vWorldSatellite']",
            urlDefine: function (vWorldURL) {
                return `vWorldURL : '${vWorldURL}'`;
            }
        }
    },

    CUSTOM: {
        type: 'custom',
        maps: {
            base: 'esri_lrc_cache',
            air: 'esri_lrc_cache',
            white: 'esri_lrc_cache',
            color: 'esri_lrc_cache',
            etc: 'esri_lrc_cache'
        },
        option: function (customConfig) {
            // customConfig는 JSON 문자열 또는 객체
            let config;
            try {
                if (typeof customConfig === 'string' && customConfig.trim()) {
                    config = JSON.parse(customConfig);
                } else if (typeof customConfig === 'object' && customConfig !== null) {
                    config = customConfig;
                } else {
                    // 기본 설정 사용
                    config = window.BasemapConfigs.CUSTOM.defaultConfig;
                }
            } catch (e) {
                console.warn('CUSTOM basemap config parsing failed, using default:', e);
                config = window.BasemapConfigs.CUSTOM.defaultConfig;
            }

            // 템플릿 리터럴로 보기 좋게 포맷팅
            return `basemap : {
			custom: {
				esri_lrc_cache: {
					type: '${config.custom.esri_lrc_cache.type}',
					name: '${config.custom.esri_lrc_cache.name}',
					params: {
						server: {
							url: '${config.custom.esri_lrc_cache.params.server.url}',
							proxyURL: '${config.custom.esri_lrc_cache.params.server.proxyURL}',
							proxyParam: '${config.custom.esri_lrc_cache.params.server.proxyParam}'
						},
						service: '${config.custom.esri_lrc_cache.params.service}',
						projection: '${config.custom.esri_lrc_cache.params.projection}',
						tileGrid: {
							origin: [${config.custom.esri_lrc_cache.params.tileGrid.origin.join(', ')}],
							resolutions: [
								${config.custom.esri_lrc_cache.params.tileGrid.resolutions.join(',\n\t\t\t\t\t\t\t\t')}
							],
							tileSize: ${config.custom.esri_lrc_cache.params.tileGrid.tileSize}
						}
					}
				}
			}
		},`;
        },
        replacement: {
            from: "custom:[esri_lrc_cache]",
            to: "custom:['esri_lrc_cache']"
        },
        airOption: {
            basemap: "custom:['esri_lrc_cache']",
            urlDefine: function () {
                return '';  // 커스텀은 별도 URL 정의 불필요
            }
        },
        // 기본 커스텀 설정
        defaultConfig: {
            custom: {
                esri_lrc_cache: {
                    type: 'api',
                    name: 'ESRI LRC 타일 캐시',
                    params: {
                        server: {
                            url: 'http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png',
                            proxyURL: 'proxyUrl.jsp',
                            proxyParam: 'url'
                        },
                        service: 'esriLrc',
                        projection: 'EPSG:5186',
                        tileGrid: {
                            origin: [-5423200, 6394600],
                            resolutions: [
                                1058.3354500042335,  // L0
                                529.16772500211675,  // L1
                                264.58386250105838,  // L2
                                132.29193125052919,  // L3
                                66.145965625264594,  // L4
                                26.458386250105836,  // L5
                                13.229193125052918,  // L6
                                7.9375158750317505,  // L7
                                2.6458386250105836,  // L8
                                1.3229193125052918,  // L9
                                0.66145965625264591, // L10
                                0.26458386250105836  // L11
                            ],
                            tileSize: 256
                        }
                    }
                },
            }
        }
    }
};

// 유틸리티 함수들
window.BasemapUtils = {
    /**
     * 배경지도 설정 객체 조회
     * @param {string} basemapType - BAROEMAP, VWORLD, CUSTOM
     * @returns {object} 배경지도 설정 객체
     */
    getBasemapConfig: function (basemapType) {
        return window.BasemapConfigs[basemapType];
    },

    /**
     * 배경지도 타입별 변수 설정
     * @param {string} sampleBasemap - 배경지도 타입
     * @returns {object} basemapType, basemap_* 변수들
     */
    getBasemapVariables: function (sampleBasemap) {
        const config = this.getBasemapConfig(sampleBasemap);
        if (!config) {
            console.warn(`Unknown basemap type: ${sampleBasemap}`);
            // 기본값으로 VWORLD 사용
            return this.getBasemapVariables('VWORLD');
        }

        return {
            basemapType: config.type,
            basemap_base: config.maps.base,
            basemap_air: config.maps.air,
            basemap_white: config.maps.white,
            basemap_color: config.maps.color,
            basemap_etc: config.maps.etc
        };
    },

    /**
     * 배경지도 옵션 HTML 생성
     * @param {string} sampleBasemap - 배경지도 타입
     * @param {object} params - 매개변수들 (URL, Key 등)
     * @returns {string} 배경지도 옵션 HTML
     */
    generateBasemapOption: function (sampleBasemap, params = {}) {
        const config = this.getBasemapConfig(sampleBasemap);
        if (!config) {
            console.warn(`Unknown basemap type: ${sampleBasemap}`);
            return this.generateBasemapOption('VWORLD', params);
        }

        switch (sampleBasemap) {
            case 'BAROEMAP':
                return config.option(params.baroEMapURL, params.baroEMapAirURL, params.baroEMapKey);
            case 'VWORLD':
                return config.option(params.vWorldURL);
            case 'CUSTOM':
                // 커스텀 설정은 Properties에서 JSON으로 받아오거나 기본값 사용
                const customConfig = params.customConfig || config.defaultConfig;
                return config.option(customConfig);
            default:
                return config.option();
        }
    },

    /**
     * HTML 문자열 교체 처리
     * @param {string} html - 원본 HTML
     * @param {string} sampleBasemap - 배경지도 타입
     * @returns {string} 교체된 HTML
     */
    replaceBasemapInHtml: function (html, sampleBasemap) {
        const config = this.getBasemapConfig(sampleBasemap);
        if (config && config.replacement) {
            return html.replace(config.replacement.from, config.replacement.to);
        }
        return html;
    },

    /**
     * 배경지도 Air 옵션 처리
     * @param {string} sampleBasemap - 배경지도 타입
     * @param {object} params - 매개변수들
     * @returns {object} basemap, urlDefine 설정
     */
    getAirBasemapOption: function (sampleBasemap, params = {}) {
        const config = this.getBasemapConfig(sampleBasemap);
        if (!config) {
            return this.getAirBasemapOption('VWORLD', params);
        }

        const airOption = config.airOption;
        return {
            basemap: airOption.basemap,
            urlDefine: airOption.urlDefine(params.url)
        };
    }
};