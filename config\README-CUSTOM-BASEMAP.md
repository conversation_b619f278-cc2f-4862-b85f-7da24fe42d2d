# CUSTOM Basemap 환경변수 설정 가이드

이 문서는 개발자센터에서 CUSTOM basemap 설정을 외부 환경변수를 통해 주입하는 방법을 설명합니다.

## 개요

기존에는 basemap-config.js 파일에 하드코딩된 설정을 사용했지만, 이제 `CUSTOM_BASEMAP_CONFIG` 환경변수를 통해 동적으로 설정을 주입할 수 있습니다.

## 설정 방법

### 1. Properties 파일 설정

`src/main/resources/egovframework/egovProps/globals.properties` 파일에서:

```properties
# CUSTOM 배경지도 설정 - JSON 문자열로 설정 (환경변수 ${CUSTOM_BASEMAP_CONFIG}로 오버라이드 가능)
Sample.Basemap.Custom.Config = ${CUSTOM_BASEMAP_CONFIG:}
```

### 2. 환경변수 설정

#### Windows (배치 파일)
```batch
set CUSTOM_BASEMAP_CONFIG={"custom":{"esri_lrc_cache":{"type":"api","name":"ESRI LRC 타일 캐시","params":{"server":{"url":"http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675,264.58386250105838,132.29193125052919,66.145965625264594,26.458386250105836,13.229193125052918,7.9375158750317505,2.6458386250105836,1.3229193125052918,0.66145965625264591,0.26458386250105836],"tileSize":256}}}}}
```

#### Linux/Unix (셸 스크립트)
```bash
export CUSTOM_BASEMAP_CONFIG='{"custom":{"esri_lrc_cache":{"type":"api","name":"ESRI LRC 타일 캐시","params":{"server":{"url":"http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675,264.58386250105838,132.29193125052919,66.145965625264594,26.458386250105836,13.229193125052918,7.9375158750317505,2.6458386250105836,1.3229193125052918,0.66145965625264591,0.26458386250105836],"tileSize":256}}}}}'
```

#### Docker Compose
```yaml
environment:
  CUSTOM_BASEMAP_CONFIG: |
    {
      "custom": {
        "esri_lrc_cache": {
          "type": "api",
          "name": "ESRI LRC 타일 캐시",
          "params": {
            "server": {
              "url": "http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png",
              "proxyURL": "proxyUrl.jsp",
              "proxyParam": "url"
            },
            "service": "esriLrc",
            "projection": "EPSG:5186",
            "tileGrid": {
              "origin": [-5423200, 6394600],
              "resolutions": [1058.3354500042335, 529.16772500211675, ...],
              "tileSize": 256
            }
          }
        }
      }
    }
```

## JSON 구조 설명

```json
{
  "custom": {
    "esri_lrc_cache": {
      "type": "api",                    // 타일 서비스 타입
      "name": "ESRI LRC 타일 캐시",      // 표시명
      "params": {
        "server": {
          "url": "타일 서버 URL",        // {L}/{R}/{C} 패턴 사용
          "proxyURL": "proxyUrl.jsp",   // 프록시 JSP 파일
          "proxyParam": "url"           // 프록시 파라미터명
        },
        "service": "esriLrc",           // 서비스 식별자
        "projection": "EPSG:5186",      // 좌표계
        "tileGrid": {
          "origin": [-5423200, 6394600], // 타일 그리드 원점
          "resolutions": [...],          // 해상도 배열 (줌 레벨별)
          "tileSize": 256               // 타일 크기
        }
      }
    }
  }
}
```

## 사용 예시

### 1. 스크립트 실행
```bash
# Linux/Unix
chmod +x config/set-custom-basemap.sh
./config/set-custom-basemap.sh

# Windows
config\set-custom-basemap.bat
```

### 2. Docker 실행
```bash
docker-compose -f config/docker-compose.custom-basemap.yml up
```

### 3. JAR 실행 시
```bash
export CUSTOM_BASEMAP_CONFIG='...'
java -jar target/developer-center.war
```

## 검증 기능

JavaScript에서 설정 검증을 위한 유틸리티 함수들이 제공됩니다:

```javascript
// 설정 검증
const validation = BasemapUtils.validateCustomBasemapConfig(configString);
if (!validation.isValid) {
    console.error('설정 오류:', validation.errors);
}

// 환경변수에서 로드 및 검증
const config = BasemapUtils.loadAndValidateCustomConfig(envConfigString);
```

## 주의사항

1. **JSON 형식**: 환경변수는 유효한 JSON 문자열이어야 합니다.
2. **이스케이프**: 특수문자는 적절히 이스케이프되어야 합니다.
3. **한 줄 형식**: 환경변수는 개행 없는 한 줄 형식으로 설정해야 합니다.
4. **검증**: 잘못된 설정 시 기본 설정으로 자동 대체됩니다.

## 트러블슈팅

### 1. JSON 파싱 오류
- JSON 문법을 확인하세요.
- 따옴표와 이스케이프 문자를 확인하세요.

### 2. 설정이 적용되지 않음
- 환경변수가 올바르게 설정되었는지 확인하세요.
- 브라우저 개발자 도구에서 콘솔 메시지를 확인하세요.

### 3. 타일이 로드되지 않음
- 서버 URL이 올바른지 확인하세요.
- 프록시 설정이 필요한지 확인하세요.
- 좌표계와 해상도 설정을 확인하세요.
