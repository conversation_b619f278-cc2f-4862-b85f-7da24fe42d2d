version: '3.8'

services:
  developer-center:
    image: developer-center:latest
    ports:
      - "8080:8080"
    environment:
      # CUSTOM basemap 설정을 환경변수로 주입
      CUSTOM_BASEMAP_CONFIG: |
        {
          "custom": {
            "esri_lrc_cache": {
              "type": "api",
              "name": "ESRI LRC 타일 캐시",
              "params": {
                "server": {
                  "url": "http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png",
                  "proxyURL": "proxyUrl.jsp",
                  "proxyParam": "url"
                },
                "service": "esriLrc",
                "projection": "EPSG:5186",
                "tileGrid": {
                  "origin": [-5423200, 6394600],
                  "resolutions": [
                    1058.3354500042335,
                    529.16772500211675,
                    264.58386250105838,
                    132.29193125052919,
                    66.145965625264594,
                    26.458386250105836,
                    13.229193125052918,
                    7.9375158750317505,
                    2.6458386250105836,
                    1.3229193125052918,
                    0.66145965625264591,
                    0.26458386250105836
                  ],
                  "tileSize": 256
                }
              }
            }
          }
        }
      # 다른 환경변수들
      JAVA_OPTS: "-Xmx1g -Xms512m"
    volumes:
      - ./logs:/app/logs
